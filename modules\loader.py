"""
Chat loader and chunking module for ChatWhiz.
Supports multiple chat formats and provides intelligent chunking strategies.
"""

import os
import json
import re
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd


class ChatMessage:
    """Represents a single chat message."""
    
    def __init__(
        self,
        text: str,
        sender: str,
        timestamp: Optional[datetime] = None,
        message_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.text = text.strip()
        self.sender = sender
        self.timestamp = timestamp or datetime.now()
        self.message_id = message_id or self._generate_id()
        self.metadata = metadata or {}
    
    def _generate_id(self) -> str:
        """Generate a unique ID for the message."""
        content = f"{self.sender}:{self.text}:{self.timestamp.isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            'text': self.text,
            'sender': self.sender,
            'timestamp': self.timestamp.isoformat(),
            'message_id': self.message_id,
            'metadata': self.metadata
        }


class ChatChunk:
    """Represents a chunk of chat messages for embedding."""
    
    def __init__(
        self,
        messages: List[ChatMessage],
        chunk_id: Optional[str] = None
    ):
        self.messages = messages
        self.chunk_id = chunk_id or self._generate_id()
        self.text = self._combine_messages()
    
    def _generate_id(self) -> str:
        """Generate a unique ID for the chunk."""
        message_ids = [msg.message_id for msg in self.messages]
        content = "|".join(message_ids)
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def _combine_messages(self) -> str:
        """Combine messages into a single text for embedding."""
        combined_parts = []
        for msg in self.messages:
            # Format: "Sender: Message"
            combined_parts.append(f"{msg.sender}: {msg.text}")
        return "\n".join(combined_parts)
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get metadata for the chunk."""
        return {
            'chunk_id': self.chunk_id,
            'message_count': len(self.messages),
            'senders': list(set(msg.sender for msg in self.messages)),
            'start_time': min(msg.timestamp for msg in self.messages).isoformat(),
            'end_time': max(msg.timestamp for msg in self.messages).isoformat(),
            'message_ids': [msg.message_id for msg in self.messages]
        }


class ChatLoader:
    """Loads and processes chat files from various formats."""
    
    def __init__(self, chunk_size: int = 3):
        """
        Initialize chat loader.
        
        Args:
            chunk_size: Number of messages per chunk
        """
        self.chunk_size = chunk_size
    
    def load_whatsapp_export(self, file_path: str) -> List[ChatMessage]:
        """
        Load WhatsApp chat export (txt format).
        
        Expected format: "DD/MM/YYYY, HH:MM - Sender: Message"
        """
        messages = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # WhatsApp export pattern
        pattern = r'(\d{1,2}/\d{1,2}/\d{4}), (\d{1,2}:\d{2}) - ([^:]+): (.+)'
        
        for match in re.finditer(pattern, content, re.MULTILINE):
            date_str, time_str, sender, text = match.groups()
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(f"{date_str} {time_str}", "%d/%m/%Y %H:%M")
            except ValueError:
                timestamp = datetime.now()
            
            messages.append(ChatMessage(
                text=text.strip(),
                sender=sender.strip(),
                timestamp=timestamp,
                metadata={'source': 'whatsapp', 'file': os.path.basename(file_path)}
            ))
        
        return messages
    
    def load_json_chat(self, file_path: str) -> List[ChatMessage]:
        """
        Load chat from JSON format.
        
        Expected format: List of objects with 'text', 'sender', 'timestamp' fields
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        messages = []
        for item in data:
            timestamp = None
            if 'timestamp' in item:
                try:
                    timestamp = datetime.fromisoformat(item['timestamp'])
                except (ValueError, TypeError):
                    timestamp = datetime.now()
            
            messages.append(ChatMessage(
                text=item.get('text', ''),
                sender=item.get('sender', 'Unknown'),
                timestamp=timestamp,
                message_id=item.get('id'),
                metadata={
                    'source': 'json',
                    'file': os.path.basename(file_path),
                    **item.get('metadata', {})
                }
            ))
        
        return messages
    
    def load_csv_chat(self, file_path: str) -> List[ChatMessage]:
        """
        Load chat from CSV format.
        
        Expected columns: text, sender, timestamp (optional)
        """
        df = pd.read_csv(file_path)
        
        messages = []
        for _, row in df.iterrows():
            timestamp = None
            if 'timestamp' in df.columns and pd.notna(row['timestamp']):
                try:
                    timestamp = pd.to_datetime(row['timestamp']).to_pydatetime()
                except (ValueError, TypeError):
                    timestamp = datetime.now()
            
            messages.append(ChatMessage(
                text=str(row.get('text', '')),
                sender=str(row.get('sender', 'Unknown')),
                timestamp=timestamp,
                metadata={'source': 'csv', 'file': os.path.basename(file_path)}
            ))
        
        return messages
    
    def load_discord_export(self, file_path: str) -> List[ChatMessage]:
        """
        Load Discord chat export (JSON format from DiscordChatExporter).
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        messages = []
        for msg in data.get('messages', []):
            timestamp = None
            if 'timestamp' in msg:
                try:
                    timestamp = datetime.fromisoformat(msg['timestamp'].replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    timestamp = datetime.now()
            
            # Combine content and attachments
            text_parts = []
            if msg.get('content'):
                text_parts.append(msg['content'])
            
            for attachment in msg.get('attachments', []):
                if attachment.get('url'):
                    text_parts.append(f"[Attachment: {attachment.get('fileName', 'file')}]")
            
            text = ' '.join(text_parts)
            if not text:
                continue
            
            messages.append(ChatMessage(
                text=text,
                sender=msg.get('author', {}).get('name', 'Unknown'),
                timestamp=timestamp,
                message_id=msg.get('id'),
                metadata={
                    'source': 'discord',
                    'file': os.path.basename(file_path),
                    'channel': data.get('channel', {}).get('name', 'Unknown')
                }
            ))
        
        return messages
    
    def auto_detect_and_load(self, file_path: str) -> List[ChatMessage]:
        """
        Auto-detect file format and load accordingly.
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.json':
            # Try Discord format first, then generic JSON
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'messages' in data and 'channel' in data:
                    return self.load_discord_export(file_path)
                else:
                    return self.load_json_chat(file_path)
            except Exception as e:
                print(f"Error loading JSON file: {e}")
                return []
        
        elif file_ext == '.csv':
            return self.load_csv_chat(file_path)
        
        elif file_ext == '.txt':
            # Try WhatsApp format
            return self.load_whatsapp_export(file_path)
        
        else:
            print(f"Unsupported file format: {file_ext}")
            return []
    
    def create_chunks(
        self,
        messages: List[ChatMessage],
        strategy: str = "sliding_window"
    ) -> List[ChatChunk]:
        """
        Create chunks from messages using specified strategy.
        
        Args:
            messages: List of chat messages
            strategy: Chunking strategy ('fixed', 'sliding_window', 'conversation')
        """
        if not messages:
            return []
        
        if strategy == "fixed":
            return self._create_fixed_chunks(messages)
        elif strategy == "sliding_window":
            return self._create_sliding_window_chunks(messages)
        elif strategy == "conversation":
            return self._create_conversation_chunks(messages)
        else:
            raise ValueError(f"Unknown chunking strategy: {strategy}")
    
    def _create_fixed_chunks(self, messages: List[ChatMessage]) -> List[ChatChunk]:
        """Create non-overlapping fixed-size chunks."""
        chunks = []
        for i in range(0, len(messages), self.chunk_size):
            chunk_messages = messages[i:i + self.chunk_size]
            chunks.append(ChatChunk(chunk_messages))
        return chunks
    
    def _create_sliding_window_chunks(self, messages: List[ChatMessage]) -> List[ChatChunk]:
        """Create overlapping sliding window chunks."""
        chunks = []
        for i in range(len(messages) - self.chunk_size + 1):
            chunk_messages = messages[i:i + self.chunk_size]
            chunks.append(ChatChunk(chunk_messages))
        
        # Add final chunk if needed
        if len(messages) >= self.chunk_size:
            final_chunk = messages[-self.chunk_size:]
            if final_chunk != chunks[-1].messages:
                chunks.append(ChatChunk(final_chunk))
        
        return chunks
    
    def _create_conversation_chunks(self, messages: List[ChatMessage]) -> List[ChatChunk]:
        """Create chunks based on conversation breaks (time gaps)."""
        if not messages:
            return []
        
        chunks = []
        current_chunk = []
        
        for i, message in enumerate(messages):
            current_chunk.append(message)
            
            # Check if we should end the chunk
            should_end = False
            
            # End if chunk is full
            if len(current_chunk) >= self.chunk_size:
                should_end = True
            
            # End if there's a long time gap to next message
            if i < len(messages) - 1:
                time_gap = (messages[i + 1].timestamp - message.timestamp).total_seconds()
                if time_gap > 3600:  # 1 hour gap
                    should_end = True
            
            # End if last message
            if i == len(messages) - 1:
                should_end = True
            
            if should_end and current_chunk:
                chunks.append(ChatChunk(current_chunk))
                current_chunk = []
        
        return chunks
    
    def deduplicate_chunks(self, chunks: List[ChatChunk]) -> List[ChatChunk]:
        """Remove duplicate chunks based on content similarity."""
        seen_texts = set()
        unique_chunks = []
        
        for chunk in chunks:
            # Use a simplified version of the text for deduplication
            simplified_text = re.sub(r'\s+', ' ', chunk.text.lower().strip())
            
            if simplified_text not in seen_texts:
                seen_texts.add(simplified_text)
                unique_chunks.append(chunk)
        
        print(f"Deduplicated {len(chunks)} chunks to {len(unique_chunks)} unique chunks")
        return unique_chunks

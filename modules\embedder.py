"""
Embedding module for ChatWhiz using Instructor-XL model.
Provides semantic embeddings for chat messages with instruction-tuned approach.
"""

import os
import pickle
import hashlib
from typing import List, Union, Optional
import numpy as np
from InstructorEmbedding import INSTRUCTOR


class InstructorEmbedder:
    """
    Wrapper for Instructor-XL embedding model with caching support.
    """
    
    def __init__(
        self, 
        model_name: str = "hkunlp/instructor-xl",
        instruction: str = "Represent the chat message for semantic search:",
        cache_dir: str = "data/cache"
    ):
        """
        Initialize the Instructor embedder.
        
        Args:
            model_name: HuggingFace model name for Instructor
            instruction: Instruction text for embedding
            cache_dir: Directory to cache embeddings
        """
        self.model_name = model_name
        self.instruction = instruction
        self.cache_dir = cache_dir
        self.model = None
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
    def _load_model(self):
        """Lazy load the model to save memory."""
        if self.model is None:
            print(f"Loading Instructor model: {self.model_name}")
            self.model = INSTRUCTOR(self.model_name)
            print("Model loaded successfully!")
    
    def _get_cache_key(self, texts: List[str]) -> str:
        """Generate cache key for a list of texts."""
        combined_text = f"{self.instruction}|{self.model_name}|{'|'.join(texts)}"
        return hashlib.md5(combined_text.encode()).hexdigest()
    
    def _load_from_cache(self, cache_key: str) -> Optional[np.ndarray]:
        """Load embeddings from cache if available."""
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                print(f"Warning: Failed to load cache {cache_key}: {e}")
        return None
    
    def _save_to_cache(self, cache_key: str, embeddings: np.ndarray):
        """Save embeddings to cache."""
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(embeddings, f)
        except Exception as e:
            print(f"Warning: Failed to save cache {cache_key}: {e}")
    
    def encode(
        self, 
        texts: Union[str, List[str]], 
        use_cache: bool = True,
        batch_size: int = 32
    ) -> np.ndarray:
        """
        Encode texts into embeddings using Instructor model.
        
        Args:
            texts: Single text or list of texts to encode
            use_cache: Whether to use caching
            batch_size: Batch size for encoding
            
        Returns:
            numpy array of embeddings
        """
        # Handle single text input
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            return np.array([])
        
        # Check cache first
        if use_cache:
            cache_key = self._get_cache_key(texts)
            cached_embeddings = self._load_from_cache(cache_key)
            if cached_embeddings is not None:
                print(f"Loaded {len(texts)} embeddings from cache")
                return cached_embeddings
        
        # Load model if needed
        self._load_model()
        
        # Prepare instruction-text pairs
        instruction_text_pairs = [[self.instruction, text] for text in texts]
        
        # Encode in batches to manage memory
        all_embeddings = []
        for i in range(0, len(instruction_text_pairs), batch_size):
            batch = instruction_text_pairs[i:i + batch_size]
            print(f"Encoding batch {i//batch_size + 1}/{(len(instruction_text_pairs) + batch_size - 1)//batch_size}")
            batch_embeddings = self.model.encode(batch)
            all_embeddings.append(batch_embeddings)
        
        # Combine all embeddings
        embeddings = np.vstack(all_embeddings) if len(all_embeddings) > 1 else all_embeddings[0]
        
        # Save to cache
        if use_cache:
            self._save_to_cache(cache_key, embeddings)
            print(f"Saved {len(texts)} embeddings to cache")
        
        return embeddings
    
    def encode_query(self, query: str) -> np.ndarray:
        """
        Encode a single query for search.
        
        Args:
            query: Search query text
            
        Returns:
            Query embedding as numpy array
        """
        return self.encode([query], use_cache=False)[0]
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings from this model."""
        self._load_model()
        # Test with a dummy text to get dimension
        test_embedding = self.encode(["test"], use_cache=False)
        return test_embedding.shape[1]
    
    def clear_cache(self):
        """Clear all cached embeddings."""
        import glob
        cache_files = glob.glob(os.path.join(self.cache_dir, "*.pkl"))
        for cache_file in cache_files:
            try:
                os.remove(cache_file)
            except Exception as e:
                print(f"Warning: Failed to remove cache file {cache_file}: {e}")
        print(f"Cleared {len(cache_files)} cache files")


def create_embedder_from_config(config: dict) -> InstructorEmbedder:
    """
    Create an InstructorEmbedder from configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured InstructorEmbedder instance
    """
    return InstructorEmbedder(
        model_name=config.get('embedding_model', 'hkunlp/instructor-xl'),
        instruction=config.get('instruction', 'Represent the chat message for semantic search:'),
        cache_dir=config.get('cache_dir', 'data/cache')
    )
